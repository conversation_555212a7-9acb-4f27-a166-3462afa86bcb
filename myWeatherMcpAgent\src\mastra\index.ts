import { <PERSON><PERSON> } from '@mastra/core/mastra';
import { PinoLogger } from '@mastra/loggers';
import { LibSQLStore } from '@mastra/libsql';

import { weatheragent } from './agents/weather-agent';

export const mastra = new Mastra({
  agents: { weatheragent },
  storage: new LibSQLStore({
    url: ":memory:",
  }),
  logger: new PinoLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});
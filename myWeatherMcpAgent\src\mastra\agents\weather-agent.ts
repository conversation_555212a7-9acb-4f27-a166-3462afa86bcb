import { Agent } from "@mastra/core/agent";
import { MCPClient } from "@mastra/mcp";
import { openai } from "@ai-sdk/openai";

// MCPClient'ı senin configinle başlat
const mcp = new MCPClient({
  servers: {
    "weather-mcp": {
      command: "npx",
      args: [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@MehmetFatihAktas/weather-mcp",
        "--key",
        "ea16ac82-8f12-4ac1-b41b-ff0722e1cf5d"
      ]
    }
  }
});

// Agent'ı oluştur
const agent = new Agent({
  name: "Weather MCP Agent",
  instructions: "Kullanıcıya şehir bazlı hava durumu bilgisini MCP tool ile getir.",
  model: openai("gpt-4o"),
  // tools kısmını boş bırakıyoruz, toolset MCP'den dinamik gelecek
});

// Toolset'i MCP'den alıp agent'a ileterek sorgu yap
async function getWeather(city: string) {
  const toolsets = await mcp.getToolsets();
  const response = await agent.generate(
    [
      { role: "user", content: `${city} için hava durumu nedir?` }
    ],
    { toolsets }
  );
  console.log(response.text);
}

// Örnek kullanım

export const weatheragent = agent;
